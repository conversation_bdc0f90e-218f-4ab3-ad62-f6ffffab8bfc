1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.legal2025.yamy"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:41:13-72
25-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:13-50
27-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
30            <action android:name="android.intent.action.GET_CONTENT" />
30-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
30-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
31
32            <data android:mimeType="*/*" />
32-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:13-50
32-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:19-48
33        </intent>
34    </queries>
35
36    <uses-permission android:name="android.permission.WAKE_LOCK" />
36-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
36-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
37    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Permissions options for the `notification` group -->
37-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
37-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
38    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
38-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
38-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
39    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
39-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
39-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
40    <uses-permission android:name="android.permission.VIBRATE" />
40-->[:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
40-->[:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
41    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
41-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\251ae3e1e0bd6d03a4314eb3f1846e6d\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
41-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\251ae3e1e0bd6d03a4314eb3f1846e6d\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
50        android:name="android.app.Application"
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cda5eae4219c760778cd3db22bb75c66\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
52        android:debuggable="true"
53        android:extractNativeLibs="false"
54        android:icon="@mipmap/ic_launcher"
55        android:label="sharia_law_app" >
56        <activity
57            android:name="com.legal2025.yamy.MainActivity"
58            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
59            android:exported="true"
60            android:hardwareAccelerated="true"
61            android:launchMode="singleTop"
62            android:taskAffinity=""
63            android:theme="@style/LaunchTheme"
64            android:windowSoftInputMode="adjustResize" >
65
66            <!--
67                 Specifies an Android theme to apply to this Activity as soon as
68                 the Android process has started. This theme is visible to the user
69                 while the Flutter UI initializes. After that, this theme continues
70                 to determine the Window background behind the Flutter UI.
71            -->
72            <meta-data
73                android:name="io.flutter.embedding.android.NormalTheme"
74                android:resource="@style/NormalTheme" />
75
76            <intent-filter>
77                <action android:name="android.intent.action.MAIN" />
78
79                <category android:name="android.intent.category.LAUNCHER" />
80            </intent-filter>
81        </activity>
82        <!--
83             Don't delete the meta-data below.
84             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
85        -->
86        <meta-data
87            android:name="flutterEmbedding"
88            android:value="2" />
89
90        <service
90-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
91            android:name="com.google.firebase.components.ComponentDiscoveryService"
91-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
92            android:directBootAware="true"
92-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
93            android:exported="false" >
93-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
94            <meta-data
94-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
95                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
95-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
97            <meta-data
97-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
98                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
98-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
100            <meta-data
100-->[:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
101                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
101-->[:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-127
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
103            <meta-data
103-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
104                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
104-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
106            <meta-data
106-->[:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
107                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
107-->[:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
109            <meta-data
109-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
110                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
110-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
112            <meta-data
112-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
113                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
113-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
115            <meta-data
115-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
116                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
116-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
118            <meta-data
118-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
119                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
119-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a292d9a9eb863d426ff7866e66f09617\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
121            <meta-data
121-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
122                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
122-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
124            <meta-data
124-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
125                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
125-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb879d1880e441b3e772a8c480a3189c\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
127            <meta-data
127-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
128                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
128-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
130            <meta-data
130-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
131                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
131-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
133            <meta-data
133-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
134                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
134-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
136            <meta-data
136-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
137                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
137-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7af1d9ac59fa8fbd0401542cc57951b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
139            <meta-data
139-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
140                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
140-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
142            <meta-data
142-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
143                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
143-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fef243f8e1cc50669f1fa43b23845a5e\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
145            <meta-data
145-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
146                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
146-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
148            <meta-data
148-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
149                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
149-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd9499f3cc37533f56ddf04cf2650d5\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
151            <meta-data
151-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9875c2c7624febad3c0216c266207fc0\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
152                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
152-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9875c2c7624febad3c0216c266207fc0\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9875c2c7624febad3c0216c266207fc0\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
154            <meta-data
154-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
155                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
155-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
157            <meta-data
157-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9d8e7425fbf42dd6ba0bc12c62816f7\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
158                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
158-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9d8e7425fbf42dd6ba0bc12c62816f7\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9d8e7425fbf42dd6ba0bc12c62816f7\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
160        </service>
161        <service
161-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
162            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
162-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
163            android:exported="false"
163-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
164            android:permission="android.permission.BIND_JOB_SERVICE" />
164-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
165        <service
165-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
166            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
166-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
167            android:exported="false" >
167-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
168            <intent-filter>
168-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
169                <action android:name="com.google.firebase.MESSAGING_EVENT" />
169-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
169-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
170            </intent-filter>
171        </service>
172
173        <receiver
173-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
174            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
174-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
175            android:exported="true"
175-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
176            android:permission="com.google.android.c2dm.permission.SEND" >
176-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
177            <intent-filter>
177-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
178                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
178-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
178-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
179            </intent-filter>
180        </receiver>
181
182        <provider
182-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
183            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
183-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
184            android:authorities="com.legal2025.yamy.flutterfirebasemessaginginitprovider"
184-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
185            android:exported="false"
185-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
186            android:initOrder="99" />
186-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
187
188        <activity
188-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
189            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
189-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
190            android:excludeFromRecents="true"
190-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
191            android:exported="true"
191-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
192            android:launchMode="singleTask"
192-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
193            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
193-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
194            <intent-filter>
194-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
195                <action android:name="android.intent.action.VIEW" />
195-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
195-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
196
197                <category android:name="android.intent.category.DEFAULT" />
197-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
197-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
198                <category android:name="android.intent.category.BROWSABLE" />
198-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
198-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
199
200                <data
200-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:13-50
201                    android:host="firebase.auth"
202                    android:path="/"
203                    android:scheme="genericidp" />
204            </intent-filter>
205        </activity>
206        <activity
206-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
207            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
207-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
208            android:excludeFromRecents="true"
208-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
209            android:exported="true"
209-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
210            android:launchMode="singleTask"
210-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
211            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
211-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
212            <intent-filter>
212-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
213                <action android:name="android.intent.action.VIEW" />
213-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
213-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
214
215                <category android:name="android.intent.category.DEFAULT" />
215-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
215-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
216                <category android:name="android.intent.category.BROWSABLE" />
216-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
216-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4d9b43b6525d578c3582e2b1fec3fe\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
217
218                <data
218-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:13-50
219                    android:host="firebase.auth"
220                    android:path="/"
221                    android:scheme="recaptcha" />
222            </intent-filter>
223        </activity>
224
225        <receiver
225-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
226            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
226-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
227            android:exported="true"
227-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
228            android:permission="com.google.android.c2dm.permission.SEND" >
228-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
229            <intent-filter>
229-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
230                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
230-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
230-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
231            </intent-filter>
232
233            <meta-data
233-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
234                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
234-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
235                android:value="true" />
235-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
236        </receiver>
237        <!--
238             FirebaseMessagingService performs security checks at runtime,
239             but set to not exported to explicitly avoid allowing another app to call it.
240        -->
241        <service
241-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
242            android:name="com.google.firebase.messaging.FirebaseMessagingService"
242-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
243            android:directBootAware="true"
243-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
244            android:exported="false" >
244-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6961c66792b0baae41a4bb36fd4134d\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
245            <intent-filter android:priority="-500" >
245-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
246                <action android:name="com.google.firebase.MESSAGING_EVENT" />
246-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
246-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
247            </intent-filter>
248        </service>
249
250        <provider
250-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
251            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
251-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
252            android:authorities="com.legal2025.yamy.flutter.image_provider"
252-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
253            android:exported="false"
253-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
254            android:grantUriPermissions="true" >
254-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
255            <meta-data
255-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
256                android:name="android.support.FILE_PROVIDER_PATHS"
256-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
257                android:resource="@xml/flutter_image_picker_file_paths" />
257-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
258        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
259        <service
259-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
260            android:name="com.google.android.gms.metadata.ModuleDependencies"
260-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
261            android:enabled="false"
261-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
262            android:exported="false" >
262-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
263            <intent-filter>
263-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
264                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
264-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
264-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
265            </intent-filter>
266
267            <meta-data
267-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
268                android:name="photopicker_activity:0:required"
268-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
269                android:value="" />
269-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
270        </service>
271
272        <activity
272-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
273            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
273-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
274            android:exported="false"
274-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
275            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
275-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
276
277        <service
277-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
278            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
278-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
279            android:enabled="true"
279-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
280            android:exported="false" >
280-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
281            <meta-data
281-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
282                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
282-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
283                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
283-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
284        </service>
285
286        <activity
286-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
287            android:name="androidx.credentials.playservices.HiddenActivity"
287-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
288            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
288-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
289            android:enabled="true"
289-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
290            android:exported="false"
290-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
291            android:fitsSystemWindows="true"
291-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
292            android:theme="@style/Theme.Hidden" >
292-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a78491001511f82c2595d244d8cb59d\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
293        </activity>
294        <activity
294-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
295            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
295-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
296            android:excludeFromRecents="true"
296-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
297            android:exported="false"
297-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
298            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
298-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
299        <!--
300            Service handling Google Sign-In user revocation. For apps that do not integrate with
301            Google Sign-In, this service will never be started.
302        -->
303        <service
303-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
304            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
304-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
305            android:exported="true"
305-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
306            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
306-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
307            android:visibleToInstantApps="true" />
307-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a904d3e3eacb8c3cce4919673a09b837\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
308
309        <provider
309-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
310            android:name="com.google.firebase.provider.FirebaseInitProvider"
310-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
311            android:authorities="com.legal2025.yamy.firebaseinitprovider"
311-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
312            android:directBootAware="true"
312-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
313            android:exported="false"
313-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
314            android:initOrder="100" />
314-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33407928b1ff45454297c1e1a94abf6\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
315
316        <activity
316-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f8b51821a67bbb5ffa84ff59d703db\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
317            android:name="com.google.android.gms.common.api.GoogleApiActivity"
317-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f8b51821a67bbb5ffa84ff59d703db\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
318            android:exported="false"
318-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f8b51821a67bbb5ffa84ff59d703db\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
319            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
319-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f8b51821a67bbb5ffa84ff59d703db\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
320
321        <provider
321-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
322            android:name="androidx.startup.InitializationProvider"
322-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
323            android:authorities="com.legal2025.yamy.androidx-startup"
323-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
324            android:exported="false" >
324-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
325            <meta-data
325-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
326                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
326-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
327                android:value="androidx.startup" />
327-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d5f1e97a552c04cf800b7b0ad4bccda\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
328            <meta-data
328-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
329                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
329-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
330                android:value="androidx.startup" />
330-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
331        </provider>
332
333        <uses-library
333-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
334            android:name="androidx.window.extensions"
334-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
335            android:required="false" />
335-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
336        <uses-library
336-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
337            android:name="androidx.window.sidecar"
337-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
338            android:required="false" />
338-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5246fa6856d03ebbc5d329a5f3859c1\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
339
340        <meta-data
340-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4f54f2e8e5114b90f7f6b0790bbfac\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
341            android:name="com.google.android.gms.version"
341-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4f54f2e8e5114b90f7f6b0790bbfac\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
342            android:value="@integer/google_play_services_version" />
342-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f4f54f2e8e5114b90f7f6b0790bbfac\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
343
344        <receiver
344-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
345            android:name="androidx.profileinstaller.ProfileInstallReceiver"
345-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
346            android:directBootAware="false"
346-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
347            android:enabled="true"
347-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
348            android:exported="true"
348-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
349            android:permission="android.permission.DUMP" >
349-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
350            <intent-filter>
350-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
351                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
352            </intent-filter>
353            <intent-filter>
353-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
354                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
354-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
354-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
355            </intent-filter>
356            <intent-filter>
356-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
357                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
357-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
357-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
358            </intent-filter>
359            <intent-filter>
359-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
360                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
360-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
360-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa917d030eddf7af8909ba2e344f6195\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
361            </intent-filter>
362        </receiver>
363
364        <service
364-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
365            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
365-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
366            android:exported="false" >
366-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
367            <meta-data
367-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
368                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
368-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
369                android:value="cct" />
369-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc67733266f25c9069d64d290f20b8c4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
370        </service>
371        <service
371-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
372            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
372-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
373            android:exported="false"
373-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
374            android:permission="android.permission.BIND_JOB_SERVICE" >
374-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
375        </service>
376
377        <receiver
377-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
378            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
378-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
379            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
379-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e72322cfaf94a36292fb7a6522f0e4d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
380        <activity
380-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db123331b7ad319a2873a64ca6cf5713\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
381            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
381-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db123331b7ad319a2873a64ca6cf5713\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
382            android:exported="false"
382-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db123331b7ad319a2873a64ca6cf5713\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
383            android:stateNotNeeded="true"
383-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db123331b7ad319a2873a64ca6cf5713\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
384            android:theme="@style/Theme.PlayCore.Transparent" />
384-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db123331b7ad319a2873a64ca6cf5713\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
385    </application>
386
387</manifest>
