import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../models/subject.dart';
import '../models/pdf_model.dart';
import '../theme/app_theme.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import '../services/test_data_service.dart';
import '../services/realtime_pdf_service.dart';
import '../utils/realtime_db_tester.dart';
import '../test_realtime_simple.dart';
import 'pdf_viewer_screen.dart';

class PDFListScreen extends StatefulWidget {
  final Subject subject;
  final String category;

  const PDFListScreen({
    super.key,
    required this.subject,
    required this.category,
  });

  @override
  State<PDFListScreen> createState() => _PDFListScreenState();
}

class _PDFListScreenState extends State<PDFListScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOutCubic,
      ),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// التحقق من صلاحيات الأدمن
  bool _isAdmin() {
    final authProvider = context.read<AuthProvider>();
    return authProvider.firebaseUser?.email == '<EMAIL>';
  }

  /// عرض خيارات الأدمن
  void _showAdminOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return Container(
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // مؤشر السحب
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // عنوان
                    Text(
                      'إدارة ${widget.category} - ${widget.subject.arabicName}',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),

                    // خيارات الأدمن
                    _buildAdminOption(
                      icon: Icons.add_circle_outline,
                      title: 'إضافة ملف PDF',
                      subtitle: 'إضافة ملف جديد لهذا القسم',
                      color: const Color(0xFF10B981),
                      onTap: () {
                        Navigator.pop(context);
                        _navigateToAddPDF();
                      },
                      themeProvider: themeProvider,
                    ),
                    const SizedBox(height: 12),

                    _buildAdminOption(
                      icon: Icons.science_outlined,
                      title: 'إضافة بيانات تجريبية',
                      subtitle: 'إضافة ملفات PDF تجريبية للاختبار',
                      color: const Color(0xFF3B82F6),
                      onTap: () {
                        Navigator.pop(context);
                        _addTestData();
                      },
                      themeProvider: themeProvider,
                    ),
                    const SizedBox(height: 12),

                    _buildAdminOption(
                      icon: Icons.delete_sweep_outlined,
                      title: 'حذف البيانات التجريبية',
                      subtitle: 'حذف جميع الملفات التجريبية',
                      color: const Color(0xFFEF4444),
                      onTap: () {
                        Navigator.pop(context);
                        _removeTestData();
                      },
                      themeProvider: themeProvider,
                    ),
                    const SizedBox(height: 12),

                    _buildAdminOption(
                      icon: Icons.storage_outlined,
                      title: '🔥 اختبار Realtime Database',
                      subtitle: 'فحص شامل والتأكد من الرفع على Firebase',
                      color: const Color(0xFFFF6B35),
                      onTap: () {
                        Navigator.pop(context);
                        _testRealtimeDatabase();
                      },
                      themeProvider: themeProvider,
                    ),
                    const SizedBox(height: 12),

                    _buildAdminOption(
                      icon: Icons.flash_on_outlined,
                      title: '⚡ اختبار سريع',
                      subtitle: 'اختبار بسيط وسريع للاتصال والإضافة',
                      color: const Color(0xFF10B981),
                      onTap: () {
                        Navigator.pop(context);
                        _runQuickTest();
                      },
                      themeProvider: themeProvider,
                    ),
                    const SizedBox(height: 12),

                    _buildAdminOption(
                      icon: Icons.bug_report_outlined,
                      title: '🔍 فحص البيانات',
                      subtitle: 'فحص البيانات في Firebase وتشخيص المشاكل',
                      color: const Color(0xFFEF4444),
                      onTap: () {
                        Navigator.pop(context);
                        _debugDataCheck();
                      },
                      themeProvider: themeProvider,
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              );
            },
          ),
    );
  }

  /// بناء خيار أدمن
  Widget _buildAdminOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    required ThemeProvider themeProvider,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF334155)
                      : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            textDirection: TextDirection.rtl,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                    ),
                    Text(
                      subtitle,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF64748B)
                        : const Color(0xFF9CA3AF),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// إضافة PDF مباشرة
  void _navigateToAddPDF() {
    _showAddPDFDialog();
  }

  /// عرض نافذة إضافة PDF
  void _showAddPDFDialog() {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController linkController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return StatefulBuilder(
                builder: (context, setState) {
                  return AlertDialog(
                    backgroundColor:
                        themeProvider.isDarkMode
                            ? const Color(0xFF1E293B)
                            : Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    title: Row(
                      textDirection: TextDirection.rtl,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(
                              0xFF10B981,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.add_circle_outline,
                            color: const Color(0xFF10B981),
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'إضافة ملف PDF جديد',
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.w700,
                              fontSize: 18,
                              color:
                                  themeProvider.isDarkMode
                                      ? Colors.white
                                      : const Color(0xFF1F2937),
                            ),
                          ),
                        ),
                      ],
                    ),
                    content: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // اسم الملف
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF334155)
                                      : const Color(0xFFF8FAFC),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: const Color(
                                  0xFF10B981,
                                ).withValues(alpha: 0.2),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  textDirection: TextDirection.rtl,
                                  children: [
                                    Icon(
                                      Icons.edit_outlined,
                                      color: const Color(0xFF10B981),
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'اسم الملف',
                                      style: GoogleFonts.cairo(
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF10B981),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                TextField(
                                  controller: nameController,
                                  style: GoogleFonts.cairo(
                                    color:
                                        themeProvider.isDarkMode
                                            ? Colors.white
                                            : const Color(0xFF1F2937),
                                  ),
                                  decoration: InputDecoration(
                                    hintText: 'مثال: أسئلة منتصف الترم',
                                    hintStyle: GoogleFonts.cairo(
                                      color:
                                          themeProvider.isDarkMode
                                              ? const Color(0xFF64748B)
                                              : const Color(0xFF9CA3AF),
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide.none,
                                    ),
                                    filled: true,
                                    fillColor:
                                        themeProvider.isDarkMode
                                            ? const Color(0xFF1E293B)
                                            : Colors.white,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),

                          // رابط الملف
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF334155)
                                      : const Color(0xFFF8FAFC),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: const Color(
                                  0xFF3B82F6,
                                ).withValues(alpha: 0.2),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  textDirection: TextDirection.rtl,
                                  children: [
                                    Icon(
                                      Icons.link_outlined,
                                      color: const Color(0xFF3B82F6),
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'رابط الملف',
                                      style: GoogleFonts.cairo(
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF3B82F6),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                TextField(
                                  controller: linkController,
                                  style: GoogleFonts.cairo(
                                    color:
                                        themeProvider.isDarkMode
                                            ? Colors.white
                                            : const Color(0xFF1F2937),
                                  ),
                                  decoration: InputDecoration(
                                    hintText:
                                        'https://drive.google.com/uc?id=YOUR_FILE_ID',
                                    hintStyle: GoogleFonts.cairo(
                                      color:
                                          themeProvider.isDarkMode
                                              ? const Color(0xFF64748B)
                                              : const Color(0xFF9CA3AF),
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide.none,
                                    ),
                                    filled: true,
                                    fillColor:
                                        themeProvider.isDarkMode
                                            ? const Color(0xFF1E293B)
                                            : Colors.white,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'إلغاء',
                          style: GoogleFonts.cairo(
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF94A3B8)
                                    : const Color(0xFF6B7280),
                          ),
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          if (nameController.text.trim().isNotEmpty &&
                              linkController.text.trim().isNotEmpty) {
                            _addPDFToList(
                              nameController.text.trim(),
                              linkController.text.trim(),
                            );
                            Navigator.pop(context);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'يرجى إدخال اسم الملف ورابط صحيح',
                                  style: GoogleFonts.cairo(),
                                ),
                                backgroundColor: const Color(0xFFEF4444),
                                behavior: SnackBarBehavior.floating,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF10B981),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: Text(
                          'إضافة الملف',
                          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ],
                  );
                },
              );
            },
          ),
    );
  }

  /// إضافة PDF إلى Firestore
  void _addPDFToList(String name, String url) async {
    if (!mounted) return;

    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(_getCategoryColor()),
              ),
            ),
      );

      // إضافة الملف إلى Realtime Database بدلاً من Firestore
      final success = await RealtimePDFService.addPDF(
        name: name.trim(),
        url: url.trim(),
        category: widget.category,
        subjectId: widget.subject.id,
        adminEmail: '<EMAIL>',
      );

      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.pop(context);

      if (success) {
        // إعادة تحميل البيانات لإظهار الملف الجديد
        if (mounted) {
          setState(() {
            // تحديث الواجهة - StreamBuilder سيتحديث تلقائياً
          });

          // إضافة تأخير بسيط للتأكد من وصول البيانات
          await Future.delayed(const Duration(milliseconds: 500));
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم إضافة الملف بنجاح: $name',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: const Color(0xFF10B981),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في إضافة الملف', style: GoogleFonts.cairo()),
              backgroundColor: const Color(0xFFEF4444),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إضافة الملف', style: GoogleFonts.cairo()),
            backgroundColor: const Color(0xFFEF4444),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      // إخفاء مؤشر التحميل في حالة الخطأ
      Navigator.pop(context);

      if (kDebugMode) {
        print('❌ خطأ في إضافة PDF: $e');
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إضافة الملف: $e', style: GoogleFonts.cairo()),
          backgroundColor: const Color(0xFFEF4444),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }

  /// بناء زر إضافة PDF للأدمن
  Widget _buildAddPDFButton() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _navigateToAddPDF,
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color(0xFF10B981).withValues(alpha: 0.3),
                    width: 2,
                    style: BorderStyle.solid,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(
                        alpha: themeProvider.isDarkMode ? 0.3 : 0.08,
                      ),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Row(
                  textDirection: TextDirection.rtl,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF10B981).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.add_circle_outline,
                        color: const Color(0xFF10B981),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'إضافة ملف PDF جديد',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF10B981),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'اضغط لإضافة ملف جديد في قسم ${widget.category}',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF94A3B8)
                                      : const Color(0xFF6B7280),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getCategoryColor() {
    switch (widget.category) {
      case 'أسئلة':
        return const Color(0xFF3B82F6);
      case 'امتحانات':
        return const Color(0xFFEF4444);
      case 'ملخصات':
        return const Color(0xFF10B981);
      case 'الكتاب الرسمي':
        return const Color(0xFF8B5CF6);
      case 'أشهر المواضع':
        return const Color(0xFFFFB800);
      default:
        return AppTheme.primaryColor;
    }
  }

  IconData _getCategoryIcon() {
    switch (widget.category) {
      case 'أسئلة':
        return Icons.quiz_rounded;
      case 'امتحانات':
        return Icons.assignment_rounded;
      case 'ملخصات':
        return Icons.summarize_rounded;
      case 'الكتاب الرسمي':
        return Icons.menu_book_rounded;
      case 'أشهر المواضع':
        return Icons.star_rounded;
      default:
        return Icons.picture_as_pdf;
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoryColor = _getCategoryColor();

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: CustomScrollView(
              slivers: [
                // شريط التطبيق المخصص
                SliverAppBar(
                  expandedHeight: 200,
                  floating: false,
                  pinned: true,
                  backgroundColor: categoryColor,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            categoryColor,
                            categoryColor.withValues(alpha: 0.8),
                          ],
                        ),
                      ),
                      child: SafeArea(
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // أيقونة الفئة
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 2,
                                  ),
                                ),
                                child: Icon(
                                  _getCategoryIcon(),
                                  color: Colors.white,
                                  size: 28,
                                ),
                              ),
                              const SizedBox(height: 16),

                              // عنوان الفئة
                              Text(
                                widget.category,
                                style: GoogleFonts.cairo(
                                  fontSize: 24,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 4),

                              // اسم المادة
                              Text(
                                widget.subject.arabicName,
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  leading: IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.arrow_back_ios_rounded,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                  actions: [
                    if (_isAdmin())
                      Padding(
                        padding: const EdgeInsets.only(left: 16),
                        child: IconButton(
                          onPressed: _showAdminOptions,
                          icon: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.admin_panel_settings,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),

                // قائمة الملفات من Realtime Database
                SliverPadding(
                  padding: const EdgeInsets.all(20),
                  sliver: StreamBuilder<List<Map<String, dynamic>>>(
                    stream: RealtimePDFService.watchPDFs(
                      subjectId: widget.subject.id,
                      category: widget.category,
                    ),
                    builder: (
                      context,
                      AsyncSnapshot<List<Map<String, dynamic>>> snapshot,
                    ) {
                      // طباعة معلومات التشخيص
                      if (kDebugMode) {
                        print('🔍 Realtime StreamBuilder Debug:');
                        print('📋 Subject: ${widget.subject.id}');
                        print('📂 Category: ${widget.category}');
                        print('🔗 State: ${snapshot.connectionState}');
                        print('📊 Has Data: ${snapshot.hasData}');
                        print('❌ Has Error: ${snapshot.hasError}');
                        if (snapshot.hasData) {
                          print('📄 Count: ${snapshot.data?.length ?? 0}');
                        }
                        if (snapshot.hasError) {
                          print('❌ Error: ${snapshot.error}');
                        }
                      }

                      // معالجة حالة الانتظار
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return SliverToBoxAdapter(
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.all(50),
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  _getCategoryColor(),
                                ),
                              ),
                            ),
                          ),
                        );
                      }

                      // معالجة الأخطاء
                      if (snapshot.hasError) {
                        if (kDebugMode) {
                          print('🚨 StreamBuilder Error: ${snapshot.error}');
                        }
                        return SliverToBoxAdapter(
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.all(20),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.error_outline,
                                    size: 48,
                                    color: Colors.red[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'خطأ في تحميل الملفات',
                                    style: GoogleFonts.cairo(
                                      color: Colors.red,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'تحقق من قواعد Firebase والاتصال',
                                    style: GoogleFonts.cairo(
                                      color: Colors.grey[600],
                                      fontSize: 12,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }

                      // معالجة البيانات الفارغة أو null
                      if (!snapshot.hasData || snapshot.data == null) {
                        return SliverToBoxAdapter(
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.all(50),
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  _getCategoryColor(),
                                ),
                              ),
                            ),
                          ),
                        );
                      }

                      // تحويل البيانات إلى PDFModel بأمان
                      final List<PDFModel> pdfs = [];
                      try {
                        if (snapshot.data != null) {
                          for (final pdfData in snapshot.data!) {
                            try {
                              final pdf = PDFModel.fromJson(pdfData);
                              if (pdf.isActive) {
                                pdfs.add(pdf);
                              }
                            } catch (e) {
                              if (kDebugMode) {
                                print('❌ خطأ في تحويل PDF: $e');
                                print('📄 البيانات: $pdfData');
                              }
                            }
                          }
                        }
                      } catch (e) {
                        if (kDebugMode) {
                          print('❌ خطأ في معالجة البيانات: $e');
                        }
                        return SliverToBoxAdapter(
                          child: Center(
                            child: Text(
                              'خطأ في معالجة البيانات',
                              style: GoogleFonts.cairo(color: Colors.red),
                            ),
                          ),
                        );
                      }

                      // ترتيب الملفات حسب تاريخ الإنشاء
                      pdfs.sort((a, b) => b.createdAt.compareTo(a.createdAt));

                      if (pdfs.isEmpty && !_isAdmin()) {
                        return SliverToBoxAdapter(
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.folder_open,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'لا توجد ملفات متاحة في قسم ${widget.category} حالياً',
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    color: Colors.grey[600],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      return SliverList(
                        delegate: SliverChildBuilderDelegate((context, index) {
                          // إضافة زر الأدمن في النهاية
                          if (index == pdfs.length && _isAdmin()) {
                            return _buildAddPDFButton();
                          }

                          final pdf = pdfs[index];
                          return _buildPDFCardFromModel(pdf, index);
                        }, childCount: pdfs.length + (_isAdmin() ? 1 : 0)),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPDFCardFromModel(PDFModel pdf, int index) {
    final categoryColor = _getCategoryColor();

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return AnimatedContainer(
          duration: Duration(milliseconds: 300 + (index * 100)),
          curve: Curves.easeInOutCubic,
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(16),
            border:
                themeProvider.isDarkMode
                    ? Border.all(color: const Color(0xFF334155), width: 1)
                    : null,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: themeProvider.isDarkMode ? 0.3 : 0.08,
                ),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _openPDFFromModel(pdf),
              onLongPress:
                  _isAdmin()
                      ? () => _showPDFOptionsFromModel(pdf, index)
                      : null,
              borderRadius: BorderRadius.circular(16),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  textDirection: TextDirection.rtl,
                  children: [
                    // أيقونة PDF
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            categoryColor,
                            categoryColor.withValues(alpha: 0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: categoryColor.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.picture_as_pdf_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),

                    // معلومات الملف
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            pdf.name,
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.w700,
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFFF1F5F9)
                                      : const Color(0xFF1F2937),
                              fontSize: 16,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 6),
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: categoryColor.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  'PDF',
                                  style: GoogleFonts.cairo(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: categoryColor,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'جاهز للعرض',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color:
                                      themeProvider.isDarkMode
                                          ? const Color(0xFF94A3B8)
                                          : const Color(0xFF6B7280),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // أزرار العمليات
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // زر التحميل
                        FutureBuilder<bool>(
                          future: DownloadManager.isFileDownloaded(pdf.url),
                          builder: (context, snapshot) {
                            final isDownloaded = snapshot.data ?? false;
                            return Container(
                              margin: const EdgeInsets.only(left: 8),
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap:
                                      () => _handleDownload(pdf, isDownloaded),
                                  borderRadius: BorderRadius.circular(12),
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color:
                                          isDownloaded
                                              ? Colors.green.withValues(
                                                alpha: 0.1,
                                              )
                                              : themeProvider.isDarkMode
                                              ? const Color(0xFF334155)
                                              : const Color(0xFFF3F4F6),
                                      borderRadius: BorderRadius.circular(12),
                                      border:
                                          isDownloaded
                                              ? Border.all(
                                                color: Colors.green.withValues(
                                                  alpha: 0.3,
                                                ),
                                                width: 1,
                                              )
                                              : null,
                                    ),
                                    child: Icon(
                                      isDownloaded
                                          ? Icons.download_done_rounded
                                          : Icons.download_rounded,
                                      color:
                                          isDownloaded
                                              ? Colors.green
                                              : themeProvider.isDarkMode
                                              ? const Color(0xFF94A3B8)
                                              : const Color(0xFF6B7280),
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                        // سهم الفتح
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF334155)
                                    : const Color(0xFFF3F4F6),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.arrow_forward_ios_rounded,
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF94A3B8)
                                    : const Color(0xFF6B7280),
                            size: 16,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// فتح ملف PDF من نموذج PDFModel
  void _openPDFFromModel(PDFModel pdf) {
    if (kDebugMode) {
      print('🔍 فتح PDF:');
      print('📄 اسم الملف: ${pdf.name}');
      print('🔗 الرابط: ${pdf.url}');
      print('📂 المادة: ${widget.subject.name}');
      print('📋 الفئة: ${widget.category}');
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => PDFViewerScreen(
              subject: widget.subject,
              pdfModel: pdf,
              category: widget.category,
            ),
      ),
    );
  }

  /// عرض خيارات تعديل/حذف PDF من نموذج PDFModel
  void _showPDFOptionsFromModel(PDFModel pdf, int index) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return Container(
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // مؤشر السحب
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // عنوان
                    Text(
                      'إدارة الملف',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      pdf.name,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),

                    // خيارات
                    Row(
                      children: [
                        // تعديل
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              _showEditPDFDialog(pdf);
                            },
                            icon: const Icon(Icons.edit, size: 18),
                            label: Text(
                              'تعديل',
                              style: GoogleFonts.cairo(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF3B82F6),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // حذف
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              _showDeleteConfirmation(pdf);
                            },
                            icon: const Icon(Icons.delete, size: 18),
                            label: Text(
                              'حذف',
                              style: GoogleFonts.cairo(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFEF4444),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    // إلغاء
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'إلغاء',
                          style: GoogleFonts.cairo(
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF94A3B8)
                                    : const Color(0xFF6B7280),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
    );
  }

  /// عرض تأكيد حذف الملف
  void _showDeleteConfirmation(PDFModel pdf) {
    showDialog(
      context: context,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return AlertDialog(
                backgroundColor:
                    themeProvider.isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Text(
                  'تأكيد الحذف',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w700,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : const Color(0xFF1F2937),
                  ),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.warning_amber_rounded,
                      color: const Color(0xFFEF4444),
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'هل أنت متأكد من حذف هذا الملف؟',
                      style: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      pdf.name,
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.w600,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'إلغاء',
                      style: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      Navigator.pop(context);
                      await _deletePDF(pdf);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFEF4444),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'حذف',
                      style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  /// إضافة بيانات تجريبية
  void _addTestData() async {
    if (!mounted) return;

    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(_getCategoryColor()),
              ),
            ),
      );

      await TestDataService.addTestPDFs();

      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.pop(context);

      // إظهار رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم إضافة البيانات التجريبية بنجاح!',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: const Color(0xFF10B981),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    } catch (e) {
      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.pop(context);

      // إظهار رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'خطأ في إضافة البيانات التجريبية: $e',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: const Color(0xFFEF4444),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }

  /// حذف البيانات التجريبية
  void _removeTestData() async {
    if (!mounted) return;

    // إظهار تأكيد الحذف
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'تأكيد الحذف',
              style: GoogleFonts.cairo(fontWeight: FontWeight.w700),
            ),
            content: Text(
              'هل أنت متأكد من حذف جميع البيانات التجريبية؟',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFEF4444),
                ),
                child: Text(
                  'حذف',
                  style: GoogleFonts.cairo(color: Colors.white),
                ),
              ),
            ],
          ),
    );

    if (confirmed != true || !mounted) return;

    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(_getCategoryColor()),
              ),
            ),
      );

      await TestDataService.removeTestPDFs();

      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.pop(context);

      // إظهار رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم حذف البيانات التجريبية بنجاح!',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: const Color(0xFF10B981),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    } catch (e) {
      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.pop(context);

      // إظهار رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'خطأ في حذف البيانات التجريبية: $e',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: const Color(0xFFEF4444),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }

  /// اختبار Realtime Database
  void _testRealtimeDatabase() async {
    if (!mounted) return;

    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      const Color(0xFFFF6B35),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'جاري اختبار Realtime Database...',
                    style: GoogleFonts.cairo(color: Colors.white, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'فحص الاتصال والرفع والعرض',
                    style: GoogleFonts.cairo(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
      );

      // تشغيل الاختبار الشامل
      final results = await RealtimeDBTester.runFullTest();

      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.pop(context);

      // عرض النتائج
      _showRealtimeTestResults(results);
    } catch (e) {
      if (!mounted) return;

      // إخفاء مؤشر التحميل
      try {
        Navigator.pop(context);
      } catch (_) {}

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'خطأ في اختبار Realtime Database: $e',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: const Color(0xFFEF4444),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }

  /// عرض نتائج اختبار Realtime Database
  void _showRealtimeTestResults(Map<String, dynamic> results) {
    if (!mounted) return;

    // حساب النجاح العام
    int successCount = 0;
    int totalTests = results.length;

    results.forEach((key, value) {
      if (value['status'] == 'success') successCount++;
    });

    final successRate = (successCount / totalTests * 100).round();
    final isAllSuccess = successCount == totalTests;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(
                  isAllSuccess ? Icons.check_circle : Icons.warning,
                  color: isAllSuccess ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '🔥 نتائج اختبار Realtime Database',
                    style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص النتائج
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color:
                          isAllSuccess
                              ? Colors.green.shade50
                              : Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isAllSuccess ? Colors.green : Colors.orange,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'معدل النجاح: $successRate% ($successCount/$totalTests)',
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.bold,
                            color:
                                isAllSuccess
                                    ? Colors.green.shade700
                                    : Colors.orange.shade700,
                          ),
                        ),
                        if (!isAllSuccess)
                          Text(
                            'يوجد مشاكل تحتاج حل',
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.orange.shade600,
                            ),
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // تفاصيل النتائج
                  ...results.entries.map((entry) {
                    final status = entry.value['status'];
                    final message = entry.value['message'];
                    final solution = entry.value['solution'];

                    String icon;
                    Color color;
                    switch (status) {
                      case 'success':
                        icon = '✅';
                        color = Colors.green;
                        break;
                      case 'warning':
                        icon = '⚠️';
                        color = Colors.orange;
                        break;
                      case 'error':
                        icon = '❌';
                        color = Colors.red;
                        break;
                      default:
                        icon = 'ℹ️';
                        color = Colors.blue;
                    }

                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(icon, style: TextStyle(fontSize: 16)),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      entry.key.replaceAll('_', ' '),
                                      style: GoogleFonts.cairo(
                                        fontWeight: FontWeight.w600,
                                        color: color,
                                      ),
                                    ),
                                    Text(
                                      message,
                                      style: GoogleFonts.cairo(fontSize: 12),
                                    ),
                                    if (solution != null)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Text(
                                          '💡 الحل: $solution',
                                          style: GoogleFonts.cairo(
                                            fontSize: 11,
                                            color: Colors.blue,
                                            fontStyle: FontStyle.italic,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
            actions: [
              if (!isAllSuccess)
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _showRealtimeDBHelp();
                  },
                  child: Text(
                    'المساعدة',
                    style: GoogleFonts.cairo(color: Colors.blue),
                  ),
                ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  /// عرض مساعدة Realtime Database
  void _showRealtimeDBHelp() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              '🆘 مساعدة Realtime Database',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'خطوات حل المشاكل:',
                    style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),

                  _buildHelpStep(
                    '1. تحديث قواعد Realtime Database',
                    'اذهب إلى Firebase Console > Realtime Database > Rules\nاستبدل القواعد بـ:\n{\n  "rules": {\n    ".read": true,\n    ".write": true\n  }\n}',
                  ),

                  _buildHelpStep(
                    '2. التحقق من الاتصال',
                    'تأكد من الاتصال بالإنترنت\nجرب VPN إذا كان هناك حجب',
                  ),

                  _buildHelpStep(
                    '3. إعادة تشغيل التطبيق',
                    'أغلق التطبيق وأعد فتحه\nأو استخدم Hot Restart',
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('فهمت', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  /// بناء خطوة مساعدة
  Widget _buildHelpStep(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.w600,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 4),
          Text(description, style: GoogleFonts.cairo(fontSize: 12)),
        ],
      ),
    );
  }

  /// اختبار سريع لـ Realtime Database
  void _runQuickTest() async {
    if (!mounted) return;

    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      const Color(0xFF10B981),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'جاري الاختبار السريع...',
                    style: GoogleFonts.cairo(color: Colors.white, fontSize: 16),
                  ),
                ],
              ),
            ),
      );

      // تشغيل الاختبار السريع
      final results = await SimpleRealtimeTest.runAllTests();

      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.pop(context);

      // حساب النتائج
      final successCount = results.values.where((v) => v).length;
      final totalTests = results.length;
      final successRate = (successCount / totalTests * 100).round();
      final isAllSuccess = successCount == totalTests;

      // عرض النتائج
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: Row(
                children: [
                  Icon(
                    isAllSuccess ? Icons.check_circle : Icons.warning,
                    color: isAllSuccess ? Colors.green : Colors.orange,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '⚡ نتائج الاختبار السريع',
                    style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // ملخص النتائج
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color:
                          isAllSuccess
                              ? Colors.green.shade50
                              : Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isAllSuccess ? Colors.green : Colors.orange,
                        width: 2,
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          isAllSuccess
                              ? '🎉 كل شيء يعمل بشكل مثالي!'
                              : '⚠️ يوجد مشاكل',
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color:
                                isAllSuccess
                                    ? Colors.green.shade700
                                    : Colors.orange.shade700,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'معدل النجاح: $successRate% ($successCount/$totalTests)',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color:
                                isAllSuccess
                                    ? Colors.green.shade600
                                    : Colors.orange.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // تفاصيل النتائج
                  ...results.entries.map((entry) {
                    final success = entry.value;
                    final testName = entry.key.replaceAll('_', ' ');

                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          Icon(
                            success ? Icons.check_circle : Icons.error,
                            color: success ? Colors.green : Colors.red,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(testName, style: GoogleFonts.cairo()),
                          ),
                          Text(
                            success ? 'نجح' : 'فشل',
                            style: GoogleFonts.cairo(
                              color: success ? Colors.green : Colors.red,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                ],
              ),
              actions: [
                if (isAllSuccess)
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _showSuccessMessage(
                        '✅ Realtime Database يعمل بشكل مثالي! يمكنك إضافة ملفات PDF بثقة',
                      );
                    },
                    child: Text(
                      'ممتاز!',
                      style: GoogleFonts.cairo(color: Colors.green),
                    ),
                  ),
                if (!isAllSuccess)
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _testRealtimeDatabase(); // تشغيل الاختبار الشامل
                    },
                    child: Text(
                      'اختبار شامل',
                      style: GoogleFonts.cairo(color: Colors.blue),
                    ),
                  ),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text('إغلاق', style: GoogleFonts.cairo()),
                ),
              ],
            ),
      );
    } catch (e) {
      if (!mounted) return;

      // إخفاء مؤشر التحميل
      try {
        Navigator.pop(context);
      } catch (_) {}

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'خطأ في الاختبار السريع: $e',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: const Color(0xFFEF4444),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// فحص البيانات وتشخيص المشاكل
  void _debugDataCheck() async {
    if (!mounted) return;

    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      const Color(0xFFEF4444),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'جاري فحص البيانات...',
                    style: GoogleFonts.cairo(color: Colors.white, fontSize: 16),
                  ),
                ],
              ),
            ),
      );

      // فحص البيانات
      final pdfs = await RealtimePDFService.getPDFs(
        subjectId: widget.subject.id,
        category: widget.category,
      );

      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.pop(context);

      // عرض النتائج
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: Text(
                '🔍 نتائج فحص البيانات',
                style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات المسار
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue, width: 1),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'معلومات المسار:',
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'المادة: ${widget.subject.id}',
                            style: GoogleFonts.cairo(fontSize: 12),
                          ),
                          Text(
                            'الفئة: ${widget.category}',
                            style: GoogleFonts.cairo(fontSize: 12),
                          ),
                          Text(
                            'المسار: pdfs/${widget.subject.id}/${widget.category}',
                            style: GoogleFonts.cairo(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // نتائج البيانات
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color:
                            pdfs.isEmpty
                                ? Colors.orange.shade50
                                : Colors.green.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: pdfs.isEmpty ? Colors.orange : Colors.green,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'نتائج البيانات:',
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.bold,
                              color:
                                  pdfs.isEmpty
                                      ? Colors.orange.shade700
                                      : Colors.green.shade700,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'عدد الملفات: ${pdfs.length}',
                            style: GoogleFonts.cairo(fontSize: 12),
                          ),
                          if (pdfs.isEmpty)
                            Text(
                              'لا توجد ملفات في هذا المسار',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.orange.shade600,
                              ),
                            ),
                          if (pdfs.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            Text(
                              'الملفات الموجودة:',
                              style: GoogleFonts.cairo(
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                            ),
                            ...pdfs
                                .take(3)
                                .map(
                                  (pdf) => Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      '• ${pdf['name']} (${pdf['id']})',
                                      style: GoogleFonts.cairo(fontSize: 11),
                                    ),
                                  ),
                                ),
                            if (pdfs.length > 3)
                              Text(
                                '... و ${pdfs.length - 3} ملف آخر',
                                style: GoogleFonts.cairo(fontSize: 11),
                              ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                if (pdfs.isEmpty)
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _addTestPDF();
                    },
                    child: Text(
                      'إضافة ملف تجريبي',
                      style: GoogleFonts.cairo(color: Colors.blue),
                    ),
                  ),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text('إغلاق', style: GoogleFonts.cairo()),
                ),
              ],
            ),
      );
    } catch (e) {
      if (!mounted) return;

      // إخفاء مؤشر التحميل
      try {
        Navigator.pop(context);
      } catch (_) {}

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فحص البيانات: $e', style: GoogleFonts.cairo()),
          backgroundColor: const Color(0xFFEF4444),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }

  /// إضافة ملف تجريبي
  void _addTestPDF() async {
    final testName = 'ملف تجريبي ${DateTime.now().millisecondsSinceEpoch}';
    _addPDFToList(testName, 'https://www.google.com');
  }

  /// عرض نافذة تعديل PDF
  void _showEditPDFDialog(PDFModel pdf) {
    final TextEditingController nameController = TextEditingController(
      text: pdf.name,
    );
    final TextEditingController linkController = TextEditingController(
      text: pdf.url,
    );

    showDialog(
      context: context,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return AlertDialog(
                backgroundColor:
                    themeProvider.isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                title: Row(
                  textDirection: TextDirection.rtl,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.edit_outlined,
                        color: const Color(0xFF3B82F6),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'تعديل ملف PDF',
                        style: GoogleFonts.cairo(
                          fontWeight: FontWeight.w700,
                          fontSize: 18,
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.white
                                  : const Color(0xFF1F2937),
                        ),
                      ),
                    ),
                  ],
                ),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // اسم الملف
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color:
                              themeProvider.isDarkMode
                                  ? const Color(0xFF334155)
                                  : const Color(0xFFF8FAFC),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: const Color(
                              0xFF3B82F6,
                            ).withValues(alpha: 0.2),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              textDirection: TextDirection.rtl,
                              children: [
                                Icon(
                                  Icons.edit_outlined,
                                  color: const Color(0xFF3B82F6),
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'اسم الملف',
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF3B82F6),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            TextField(
                              controller: nameController,
                              style: GoogleFonts.cairo(
                                color:
                                    themeProvider.isDarkMode
                                        ? Colors.white
                                        : const Color(0xFF1F2937),
                              ),
                              decoration: InputDecoration(
                                hintText: 'اسم الملف الجديد',
                                hintStyle: GoogleFonts.cairo(
                                  color:
                                      themeProvider.isDarkMode
                                          ? const Color(0xFF64748B)
                                          : const Color(0xFF9CA3AF),
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide.none,
                                ),
                                filled: true,
                                fillColor:
                                    themeProvider.isDarkMode
                                        ? const Color(0xFF1E293B)
                                        : Colors.white,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),

                      // رابط الملف
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color:
                              themeProvider.isDarkMode
                                  ? const Color(0xFF334155)
                                  : const Color(0xFFF8FAFC),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: const Color(
                              0xFF10B981,
                            ).withValues(alpha: 0.2),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              textDirection: TextDirection.rtl,
                              children: [
                                Icon(
                                  Icons.link_outlined,
                                  color: const Color(0xFF10B981),
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'رابط الملف',
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF10B981),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            TextField(
                              controller: linkController,
                              style: GoogleFonts.cairo(
                                color:
                                    themeProvider.isDarkMode
                                        ? Colors.white
                                        : const Color(0xFF1F2937),
                              ),
                              decoration: InputDecoration(
                                hintText: 'الرابط الجديد للملف',
                                hintStyle: GoogleFonts.cairo(
                                  color:
                                      themeProvider.isDarkMode
                                          ? const Color(0xFF64748B)
                                          : const Color(0xFF9CA3AF),
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide.none,
                                ),
                                filled: true,
                                fillColor:
                                    themeProvider.isDarkMode
                                        ? const Color(0xFF1E293B)
                                        : Colors.white,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'إلغاء',
                      style: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      if (nameController.text.trim().isNotEmpty &&
                          linkController.text.trim().isNotEmpty) {
                        Navigator.pop(context);
                        await _updatePDF(
                          pdf,
                          nameController.text.trim(),
                          linkController.text.trim(),
                        );
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'يرجى إدخال اسم الملف ورابط صحيح',
                              style: GoogleFonts.cairo(),
                            ),
                            backgroundColor: const Color(0xFFEF4444),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF3B82F6),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                    child: Text(
                      'حفظ التعديل',
                      style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  /// تحديث PDF
  Future<void> _updatePDF(PDFModel pdf, String newName, String newUrl) async {
    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(_getCategoryColor()),
              ),
            ),
      );

      final success = await RealtimePDFService.updatePDF(
        subjectId: widget.subject.id,
        category: widget.category,
        pdfId: pdf.id,
        name: newName,
        url: newUrl,
      );

      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.pop(context);

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث الملف بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: const Color(0xFF10B981),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );

        // StreamBuilder سيتحدث تلقائياً لأن RealtimePDFService.watchPDFs
        // يراقب التغييرات في الوقت الفعلي
        if (kDebugMode) {
          print('✅ تم تحديث PDF بنجاح - StreamBuilder سيتحدث تلقائياً');
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحديث الملف', style: GoogleFonts.cairo()),
            backgroundColor: const Color(0xFFEF4444),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      // إخفاء مؤشر التحميل في حالة الخطأ
      Navigator.pop(context);

      if (kDebugMode) {
        print('❌ خطأ في تحديث PDF: $e');
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحديث الملف: $e', style: GoogleFonts.cairo()),
          backgroundColor: const Color(0xFFEF4444),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }

  /// حذف PDF
  Future<void> _deletePDF(PDFModel pdf) async {
    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(_getCategoryColor()),
              ),
            ),
      );

      final success = await RealtimePDFService.deletePDF(
        subjectId: widget.subject.id,
        category: widget.category,
        pdfId: pdf.id,
      );

      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.pop(context);

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم حذف الملف بنجاح: ${pdf.name}',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: const Color(0xFFEF4444),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );

        // StreamBuilder سيتحدث تلقائياً لأن RealtimePDFService.watchPDFs
        // يراقب التغييرات في الوقت الفعلي
        if (kDebugMode) {
          print('✅ تم حذف PDF بنجاح - StreamBuilder سيتحدث تلقائياً');
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حذف الملف', style: GoogleFonts.cairo()),
            backgroundColor: const Color(0xFFEF4444),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      // إخفاء مؤشر التحميل في حالة الخطأ
      Navigator.pop(context);

      if (kDebugMode) {
        print('❌ خطأ في حذف PDF: $e');
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في حذف الملف: $e', style: GoogleFonts.cairo()),
          backgroundColor: const Color(0xFFEF4444),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }
}
